{"workflow_settings": {"name": "Reddit Story Video Generator", "description": "Automated workflow to create engaging videos from Reddit stories", "version": "1.0.0", "schedule": {"enabled": true, "interval_hours": 24, "cron_expression": "0 9 * * *"}}, "reddit_config": {"subreddits": [{"name": "AmItheAsshole", "url": "https://www.reddit.com/r/AmItheAsshole/top/.json?t=day&limit=10", "enabled": true}, {"name": "NoSleep", "url": "https://www.reddit.com/r/NoSleep/top/.json?t=day&limit=10", "enabled": true}, {"name": "LetsNotMeet", "url": "https://www.reddit.com/r/LetsNotMeet/top/.json?t=day&limit=10", "enabled": true}], "filtering": {"min_score": 100, "min_comments": 20, "min_text_length": 500, "max_text_length": 10000, "max_posts_to_process": 3}, "user_agent": "n8n-reddit-scraper/1.0"}, "ai_config": {"gemini": {"model": "gemini-1.5-flash", "fallback_model": "gemini-1.5-pro", "api_endpoint": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent", "fallback_endpoint": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent", "prompt_template": "Rewrite the following Reddit story in a more immersive and listener-friendly tone for a voiceover. Keep the core story unchanged, but improve flow, suspense, and emotional grip. Make it suitable for short-form video narration. Remove any Reddit-specific formatting and references. Make it engaging for TikTok/Instagram audience:\n\nTitle: {title}\n\nStory: {story}", "max_retries": 3}, "elevenlabs": {"api_endpoint": "https://api.elevenlabs.io/v1/text-to-speech", "voice_id": "21m00Tcm4TlvDq8ikWAM", "voice_name": "<PERSON>", "model": "eleven_multilingual_v2", "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.2, "use_speaker_boost": true}, "alternative_voices": {"adam": "pNInz6obpgDQGcFmaJgB", "antoni": "ErXwobaYiN019PkySvjV", "arnold": "VR6AewLTigWG4xSOukaG", "bella": "EXAVITQu4vr4xnSDxMaL", "domi": "AZnzlk1XvdvUeBnXmlld", "elli": "MF3mGyEYCl7XYWbV9V6O"}}}, "video_config": {"minecraft_video": {"path": "videos/minecraft.mp4", "min_duration_minutes": 10, "random_start_max_seconds": 300}, "output": {"resolution": "auto", "video_codec": "libx264", "audio_codec": "aac", "quality": "medium", "format": "mp4"}, "subtitles": {"font_name": "Arial-Bold", "font_size": 28, "font_color": "&HFFFFFF&", "background_color": "&H80000000&", "alignment": 2, "border_style": 3, "outline": 2, "shadow": 1, "word_timing": "auto"}}, "directories": {"videos": "videos", "audio": "audio", "temp": "temp", "output": "output", "snapchat_ready": "snapchat-ready", "error_logs": "logs"}, "ffmpeg_config": {"video_trim_command": "ffmpeg -y -ss {start_time} -t {duration} -i {input_video} -c copy {output_video}", "audio_duration_command": "ffprobe -v quiet -show_entries format=duration -of csv=p=0 {audio_file}", "final_assembly_command": "ffmpeg -y -i {video_clip} -vf \"subtitles={subtitle_file}:force_style='{subtitle_style}'\" -i {audio_file} -map 0:v -map 1:a -c:v {video_codec} -c:a {audio_codec} -shortest {output_file}"}, "error_handling": {"max_retries": 3, "retry_delay_seconds": 5, "log_errors": true, "save_partial_results": true, "continue_on_error": false}, "api_limits": {"gemini": {"requests_per_minute": 60, "requests_per_day": 1500}, "elevenlabs": {"characters_per_month": 10000, "requests_per_minute": 20}, "reddit": {"requests_per_minute": 60, "delay_between_requests": 1}}, "content_guidelines": {"exclude_nsfw": true, "exclude_keywords": ["suicide", "self-harm", "violence"], "min_engagement_score": 150, "prefer_complete_stories": true}}