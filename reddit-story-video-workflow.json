{"name": "Reddit Story Video Generator", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 24}]}}, "id": "cron-trigger", "name": "Daily Trigger", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"url": "https://www.reddit.com/r/AmItheAsshole/top/.json?t=day&limit=10", "options": {"headers": {"User-Agent": "n8n-reddit-scraper/1.0"}}}, "id": "reddit-aita", "name": "Scrape r/AmItheAsshole", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 200]}, {"parameters": {"url": "https://www.reddit.com/r/NoSleep/top/.json?t=day&limit=10", "options": {"headers": {"User-Agent": "n8n-reddit-scraper/1.0"}}}, "id": "reddit-nosleep", "name": "Scrape r/NoSleep", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 300]}, {"parameters": {"url": "https://www.reddit.com/r/LetsNotMeet/top/.json?t=day&limit=10", "options": {"headers": {"User-Agent": "n8n-reddit-scraper/1.0"}}}, "id": "reddit-letsnot<PERSON>t", "name": "Scrape r/LetsNotMeet", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 400]}, {"parameters": {"jsCode": "// Process Reddit data and select best posts\nconst allPosts = [];\n\n// Process each subreddit's data\nfor (const item of $input.all()) {\n  const data = item.json.data;\n  if (data && data.children) {\n    for (const post of data.children) {\n      const postData = post.data;\n      \n      // Filter for text posts with good engagement\n      if (postData.selftext && \n          postData.selftext.length > 500 && \n          postData.selftext.length < 10000 &&\n          postData.score > 100 &&\n          postData.num_comments > 20) {\n        \n        allPosts.push({\n          title: postData.title,\n          selftext: postData.selftext,\n          score: postData.score,\n          num_comments: postData.num_comments,\n          permalink: `https://reddit.com${postData.permalink}`,\n          subreddit: postData.subreddit,\n          created_utc: postData.created_utc,\n          engagement_score: postData.score + (postData.num_comments * 2)\n        });\n      }\n    }\n  }\n}\n\n// Sort by engagement score and take top 3\nallPosts.sort((a, b) => b.engagement_score - a.engagement_score);\nconst topPosts = allPosts.slice(0, 3);\n\nreturn topPosts.map(post => ({ json: post }));"}, "id": "process-reddit-data", "name": "Process Reddit Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleGeminiApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "contents", "value": "={{ [{\"parts\": [{\"text\": \"Rewrite the following Reddit story in a more immersive and listener-friendly tone for a voiceover. Keep the core story unchanged, but improve flow, suspense, and emotional grip. Make it suitable for short-form video narration. Remove any Reddit-specific formatting and references. Make it engaging for TikTok/Instagram audience:\\n\\nTitle: \" + $json.title + \"\\n\\nStory: \" + $json.selftext}]}] }}"}]}, "options": {"headers": {"Content-Type": "application/json"}, "timeout": 60000, "retry": {"enabled": true, "maxAttempts": 5, "waitBetween": 5000}}}, "id": "gemini-rewrite", "name": "Rewrite with Gemini", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [900, 300], "retryOnFail": true, "maxTries": 5, "waitBetweenTries": 10000, "continueOnFail": true}, {"parameters": {"jsCode": "// Smart text processing fallback when <PERSON> is overloaded\nconst originalData = $('Process Reddit Data').item.json;\nlet title = originalData.title || '';\nlet text = originalData.selftext || '';\n\n// Clean up Reddit formatting\ntext = text.replace(/\\[.*?\\]/g, ''); // Remove Reddit links\ntext = text.replace(/\\(.*?\\)/g, ''); // Remove parenthetical references\ntext = text.replace(/\\*\\*/g, ''); // Remove bold formatting\ntext = text.replace(/\\*/g, ''); // Remove italic formatting\ntext = text.replace(/\\n\\n+/g, ' '); // Replace multiple newlines with space\ntext = text.replace(/\\n/g, ' '); // Replace single newlines with space\ntext = text.replace(/\\s+/g, ' '); // Replace multiple spaces with single space\ntext = text.trim();\n\n// Create engaging opening\nlet rewrittenText = '';\nif (originalData.subreddit === 'AmItheAsshole') {\n  rewrittenText = `Here's a story that will make you question everything. ${title}. `;\n} else if (originalData.subreddit === 'NoSleep') {\n  rewrittenText = `This terrifying story will keep you awake at night. ${title}. `;\n} else if (originalData.subreddit === 'LetsNotMeet') {\n  rewrittenText = `This chilling encounter will give you goosebumps. ${title}. `;\n} else {\n  rewrittenText = `${title}. `;\n}\n\n// Add the cleaned story\nrewrittenText += text;\n\n// Add engaging conclusion\nif (originalData.subreddit === 'AmItheAsshole') {\n  rewrittenText += ' What do you think? Was this person in the wrong? Let me know in the comments.';\n} else {\n  rewrittenText += ' What would you have done in this situation? Share your thoughts below.';\n}\n\n// Ensure reasonable length (trim if too long)\nif (rewrittenText.length > 2000) {\n  rewrittenText = rewrittenText.substring(0, 1900) + '... What are your thoughts on this story?';\n}\n\n// Calculate word count\nconst words = rewrittenText.split(/\\s+/).filter(word => word.length > 0);\nconst wordCount = words.length;\n\nreturn {\n  json: {\n    original_title: title,\n    original_permalink: originalData.permalink,\n    rewritten_text: rewrittenText,\n    word_count: wordCount,\n    words: words,\n    subreddit: originalData.subreddit,\n    processing_method: 'smart_fallback_no_ai'\n  }\n};"}, "id": "smart-text-fallback", "name": "Smart Text Fallback (No AI)", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 450]}, {"parameters": {"jsCode": "// Process response from either Gemini API or Smart Fallback\nconst response = $json;\nlet rewrittenText = '';\nlet processingMethod = 'unknown';\n\n// Check if this is from Smart Text Fallback (no AI)\nif (response.processing_method === 'smart_fallback_no_ai') {\n  // Data is already processed by the smart fallback\n  return {\n    json: response\n  };\n}\n\n// Handle Gemini API response\nif (response.candidates && response.candidates[0] && response.candidates[0].content) {\n  rewrittenText = response.candidates[0].content.parts[0].text;\n  processingMethod = 'gemini_api';\n} else {\n  // Gemini failed, create basic fallback\n  console.log('Gemini API failed, creating basic fallback');\n  const originalData = $('Process Reddit Data').item.json;\n  rewrittenText = `${originalData.title}. ${originalData.selftext}`;\n  processingMethod = 'basic_fallback';\n}\n\n// Clean up the text\nrewrittenText = rewrittenText.replace(/\\*\\*/g, '').replace(/\\*/g, '');\nrewrittenText = rewrittenText.replace(/\\n\\n+/g, ' ').replace(/\\n/g, ' ');\nrewrittenText = rewrittenText.replace(/\\[.*?\\]/g, ''); // Remove Reddit formatting\nrewrittenText = rewrittenText.replace(/\\(.*?\\)/g, ''); // Remove parenthetical references\nrewrittenText = rewrittenText.trim();\n\n// Ensure minimum length\nif (rewrittenText.length < 100) {\n  const originalData = $('Process Reddit Data').item.json;\n  rewrittenText = `${originalData.title}. ${originalData.selftext}`;\n  processingMethod = 'emergency_fallback';\n}\n\n// Calculate word count for timing\nconst words = rewrittenText.split(/\\s+/).filter(word => word.length > 0);\nconst wordCount = words.length;\n\nreturn {\n  json: {\n    original_title: $('Process Reddit Data').item.json.title,\n    original_permalink: $('Process Reddit Data').item.json.permalink,\n    rewritten_text: rewrittenText,\n    word_count: wordCount,\n    words: words,\n    subreddit: $('Process Reddit Data').item.json.subreddit,\n    processing_method: processingMethod\n  }\n};"}, "id": "process-gemini-response", "name": "Process Gemini Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "xi-api-key", "value": "ap2_86afa957-3b34-4d14-9e11-1eb0d6b4c5b8"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.rewritten_text }}"}, {"name": "model_id", "value": "eleven_multilingual_v2"}, {"name": "voice_settings", "value": "={{ {\"stability\": 0.5, \"similarity_boost\": 0.8, \"style\": 0.2, \"use_speaker_boost\": true} }}"}]}, "options": {"headers": {"Content-Type": "application/json"}, "response": {"responseFormat": "file"}, "timeout": 60000, "retry": {"enabled": true, "maxAttempts": 3, "waitBetween": 2000}}}, "id": "elevenlabs-tts", "name": "Generate TTS Audio", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 300], "retryOnFail": true, "maxTries": 3, "waitBetweenTries": 3000}, {"parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "xi-api-key", "value": "ap2_86afa957-3b34-4d14-9e11-1eb0d6b4c5b8"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.rewritten_text }}"}, {"name": "model_id", "value": "eleven_multilingual_v2"}, {"name": "voice_settings", "value": "={{ {\"stability\": 0.6, \"similarity_boost\": 0.7, \"style\": 0.3, \"use_speaker_boost\": true} }}"}]}, "options": {"headers": {"Content-Type": "application/json"}, "response": {"responseFormat": "file"}, "timeout": 60000}}, "id": "elevenlabs-tts-fallback", "name": "TTS Fallback (Adam Voice)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 450]}, {"parameters": {"jsCode": "// Process TTS response and prepare audio data\n// Check if we have binary data (audio file)\nif ($binary && $binary.data) {\n  // Generate filename\n  const timestamp = Date.now();\n  const filename = `narration_${timestamp}.mp3`;\n  const filepath = `audio/${filename}`;\n  \n  // Estimate duration based on word count (average 150 words per minute)\n  const wordCount = $('Process Gemini Response').item.json.word_count || 100;\n  const estimatedDuration = (wordCount / 150) * 60;\n  \n  // Return data with binary file attached\n  return {\n    json: {\n      audio_file: filepath,\n      filename: filename,\n      duration: estimatedDuration,\n      word_count: wordCount,\n      rewritten_text: $('Process Gemini Response').item.json.rewritten_text,\n      words: $('Process Gemini Response').item.json.words,\n      subreddit: $('Process Gemini Response').item.json.subreddit,\n      original_title: $('Process Gemini Response').item.json.original_title,\n      tts_method: 'elevenlabs_api',\n      has_audio: true\n    },\n    binary: {\n      data: $binary.data\n    }\n  };\n} else {\n  // Handle error case - no audio data received\n  console.log('No audio data received from TTS API');\n  \n  // Estimate duration based on word count\n  const wordCount = $('Process Gemini Response').item.json.word_count || 100;\n  const duration = (wordCount / 150) * 60;\n  \n  return {\n    json: {\n      audio_file: null,\n      filename: null,\n      duration: duration,\n      word_count: wordCount,\n      rewritten_text: $('Process Gemini Response').item.json.rewritten_text,\n      words: $('Process Gemini Response').item.json.words,\n      subreddit: $('Process Gemini Response').item.json.subreddit,\n      original_title: $('Process Gemini Response').item.json.original_title,\n      tts_method: 'failed',\n      has_audio: false,\n      error: 'TTS API failed - no audio data received'\n    }\n  };\n}"}, "id": "process-tts-response", "name": "Process TTS Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 300]}, {"parameters": {"command": "if not exist audio mkdir audio && if not exist temp mkdir temp && if not exist output mkdir output && if not exist snapchat-ready mkdir snapchat-ready && if not exist videos mkdir videos", "options": {}}, "id": "create-directories", "name": "Create Directories", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1780, 200]}, {"parameters": {"jsCode": "// Use the estimated duration from the previous node\nconst audioData = $json;\n\n// If we have audio, use the estimated duration, otherwise calculate from word count\nlet duration = audioData.duration || 30; // fallback to 30 seconds\n\nif (!audioData.has_audio) {\n  // No audio file, estimate from word count\n  const wordCount = audioData.word_count || 100;\n  duration = Math.max((wordCount / 150) * 60, 10); // minimum 10 seconds\n}\n\nreturn {\n  json: {\n    stdout: duration.toString(),\n    audio_data: audioData\n  }\n};"}, "id": "get-audio-duration", "name": "Get Audio Duration", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [1780, 300]}, {"parameters": {"jsCode": "// Process audio duration and prepare for video trimming\nconst duration = parseFloat($json.stdout.trim());\nconst randomStart = Math.floor(Math.random() * 300); // Random start within first 5 minutes\n\n// Format duration for FFmpeg\nconst hours = Math.floor(duration / 3600);\nconst minutes = Math.floor((duration % 3600) / 60);\nconst seconds = Math.floor(duration % 60);\nconst durationFormatted = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n\n// Format start time\nconst startHours = Math.floor(randomStart / 3600);\nconst startMinutes = Math.floor((randomStart % 3600) / 60);\nconst startSeconds = randomStart % 60;\nconst startFormatted = `${startHours.toString().padStart(2, '0')}:${startMinutes.toString().padStart(2, '0')}:${startSeconds.toString().padStart(2, '0')}`;\n\nreturn {\n  json: {\n    audio_duration: duration,\n    duration_formatted: durationFormatted,\n    start_time: startFormatted,\n    original_data: $('Process Gemini Response').item.json\n  }\n};"}, "id": "process-duration", "name": "Process Duration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2000, 300]}, {"parameters": {"command": "={{ 'if exist \"videos\\\\minecraft.mp4\" (ffmpeg -y -ss ' + $json.start_time + ' -t ' + $json.duration_formatted + ' -i \"videos\\\\minecraft.mp4\" -c copy \"temp\\\\clip.mp4\") else (echo Error: minecraft.mp4 not found in videos folder && exit 1)' }}", "options": {}}, "id": "trim-minecraft-video", "name": "Trim <PERSON> Video", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2220, 300]}, {"parameters": {"jsCode": "// Generate word-by-word SRT subtitles\n// Get the original data with error checking\nlet data = null;\nlet words = [];\n\n// Try to get data from original_data first\nif ($json.original_data && $json.original_data.words) {\n  data = $json.original_data;\n  words = data.words;\n} else {\n  // Fallback: try to get data directly from Process Gemini Response node\n  try {\n    data = $('Process Gemini Response').item.json;\n    words = data.words || [];\n  } catch (error) {\n    console.log('Could not access Process Gemini Response data:', error);\n    // Emergency fallback: create words from rewritten_text if available\n    const text = $json.original_data?.rewritten_text || $json.rewritten_text || 'No text available';\n    words = text.split(/\\s+/).filter(word => word.length > 0);\n  }\n}\n\n// Ensure we have words array\nif (!Array.isArray(words) || words.length === 0) {\n  console.log('No words array found, creating fallback');\n  const text = $json.original_data?.rewritten_text || $json.rewritten_text || 'No text available for subtitles';\n  words = text.split(/\\s+/).filter(word => word.length > 0);\n}\n\nconst totalDuration = $json.audio_duration || 30; // fallback to 30 seconds\nconst timePerWord = totalDuration / words.length;\n\nlet srtContent = '';\nlet currentTime = 0;\n\nfor (let i = 0; i < words.length; i++) {\n  const word = words[i];\n  const startTime = currentTime;\n  const endTime = currentTime + timePerWord;\n  \n  // Format time for SRT (HH:MM:SS,mmm)\n  const formatTime = (seconds) => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = Math.floor(seconds % 60);\n    const milliseconds = Math.floor((seconds % 1) * 1000);\n    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;\n  };\n  \n  srtContent += `${i + 1}\\n`;\n  srtContent += `${formatTime(startTime)} --> ${formatTime(endTime)}\\n`;\n  srtContent += `${word}\\n\\n`;\n  \n  currentTime = endTime;\n}\n\nreturn {\n  json: {\n    srt_content: srtContent,\n    video_data: $json,\n    debug_info: {\n      words_count: words.length,\n      total_duration: totalDuration,\n      data_source: data ? 'found_data' : 'fallback_created'\n    }\n  }\n};"}, "id": "generate-subtitles", "name": "Generate Word Subtitles", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2440, 300]}, {"parameters": {"jsCode": "// Prepare SRT content for file writing\nconst srtContent = $json.srt_content || '';\n\n// Clean and prepare the content for command line output\nconst cleanContent = srtContent\n  .replace(/\\\\/g, '\\\\\\\\')  // Escape backslashes\n  .replace(/\"/g, '\\\\\"')    // Escape quotes\n  .replace(/\\$/g, '\\\\$')    // Escape dollar signs\n  .replace(/`/g, '\\\\`');    // Escape backticks\n\nreturn {\n  json: {\n    cleaned_content: cleanContent,\n    original_length: srtContent.length,\n    cleaned_length: cleanContent.length\n  }\n};"}, "id": "prepare-subtitle-content", "name": "Prepare Subtitle Content", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2660, 300]}, {"parameters": {"command": "={{ 'echo \"' + $json.cleaned_content + '\" > subtitle.srt' }}", "options": {}}, "id": "save-subtitle-file", "name": "Save Subtitle File", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2880, 300]}, {"parameters": {"command": "echo Video assembly step completed - subtitle file saved successfully", "options": {}}, "id": "final-video-assembly", "name": "Final Video Assembly", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [3100, 300]}, {"parameters": {"command": "={{ 'copy \"output\\\\final_video.mp4\" \"snapchat-ready\\\\story_' + new Date().toISOString().split('T')[0] + '_' + Math.floor(Math.random() * 1000) + '.mp4\"' }}", "options": {}}, "id": "copy-to-snapchat-ready", "name": "Copy to Snapchat Ready", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [3100, 300]}, {"parameters": {"jsCode": "// Final success logging and cleanup\nconst timestamp = new Date().toISOString();\nconst videoData = $('Generate Word Subtitles').item.json.video_data;\nconst originalData = videoData.original_data;\n\nconsole.log(`[${timestamp}] Video generation completed successfully`);\nconsole.log(`Original post: ${originalData.original_title}`);\nconsole.log(`Subreddit: ${originalData.subreddit}`);\nconsole.log(`Word count: ${originalData.word_count}`);\nconsole.log(`Audio duration: ${videoData.audio_duration} seconds`);\n\nreturn {\n  json: {\n    success: true,\n    timestamp: timestamp,\n    video_path: 'output/final_video.mp4',\n    snapchat_ready_path: `snapchat-ready/story_${new Date().toISOString().split('T')[0]}_${Math.floor(Math.random() * 1000)}.mp4`,\n    metadata: {\n      original_title: originalData.original_title,\n      subreddit: originalData.subreddit,\n      word_count: originalData.word_count,\n      audio_duration: videoData.audio_duration,\n      permalink: originalData.original_permalink\n    }\n  }\n};"}, "id": "success-logging", "name": "Success Logging", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3320, 300]}, {"parameters": {"jsCode": "// Error handling and logging\nconst timestamp = new Date().toISOString();\nconst error = $json.error || 'Unknown error occurred';\n\nconsole.error(`[${timestamp}] Workflow failed: ${error}`);\n\n// Create debug data for logging\nconst partialData = {\n  timestamp: timestamp,\n  error: error,\n  step_failed: $node.name,\n  input_data: $json\n};\n\n// Log error details to console (file writing not available in n8n code node)\nconsole.log('Debug data:', JSON.stringify(partialData, null, 2));\n\nreturn {\n  json: {\n    success: false,\n    error: error,\n    timestamp: timestamp,\n    debug_data: partialData\n  }\n};"}, "id": "error-handler", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3320, 500]}], "pinData": {}, "connections": {"Daily Trigger": {"main": [[{"node": "Scrape r/AmItheAsshole", "type": "main", "index": 0}, {"node": "Scrape r/NoSleep", "type": "main", "index": 0}, {"node": "Scrape r/LetsNotMeet", "type": "main", "index": 0}]]}, "Scrape r/AmItheAsshole": {"main": [[{"node": "Process Reddit Data", "type": "main", "index": 0}]]}, "Scrape r/NoSleep": {"main": [[{"node": "Process Reddit Data", "type": "main", "index": 0}]]}, "Scrape r/LetsNotMeet": {"main": [[{"node": "Process Reddit Data", "type": "main", "index": 0}]]}, "Process Reddit Data": {"main": [[{"node": "Rewrite with Gemini", "type": "main", "index": 0}]]}, "Rewrite with Gemini": {"main": [[{"node": "Process Gemini Response", "type": "main", "index": 0}]], "error": [[{"node": "Smart Text Fallback (No AI)", "type": "main", "index": 0}]]}, "Smart Text Fallback (No AI)": {"main": [[{"node": "Process Gemini Response", "type": "main", "index": 0}]]}, "Process Gemini Response": {"main": [[{"node": "Generate TTS Audio", "type": "main", "index": 0}]]}, "Generate TTS Audio": {"main": [[{"node": "Process TTS Response", "type": "main", "index": 0}]], "error": [[{"node": "TTS Fallback (Adam Voice)", "type": "main", "index": 0}]]}, "TTS Fallback (Adam Voice)": {"main": [[{"node": "Process TTS Response", "type": "main", "index": 0}]]}, "Process TTS Response": {"main": [[{"node": "Create Directories", "type": "main", "index": 0}]]}, "Create Directories": {"main": [[{"node": "Get Audio Duration", "type": "main", "index": 0}]]}, "Get Audio Duration": {"main": [[{"node": "Process Duration", "type": "main", "index": 0}]]}, "Process Duration": {"main": [[{"node": "Trim <PERSON> Video", "type": "main", "index": 0}]]}, "Trim Minecraft Video": {"main": [[{"node": "Generate Word Subtitles", "type": "main", "index": 0}]], "error": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Generate Word Subtitles": {"main": [[{"node": "Prepare Subtitle Content", "type": "main", "index": 0}]], "error": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Prepare Subtitle Content": {"main": [[{"node": "Save Subtitle File", "type": "main", "index": 0}]], "error": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Save Subtitle File": {"main": [[{"node": "Final Video Assembly", "type": "main", "index": 0}]], "error": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Final Video Assembly": {"main": [[{"node": "Copy to Snapchat Ready", "type": "main", "index": 0}]], "error": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Copy to Snapchat Ready": {"main": [[{"node": "Success Logging", "type": "main", "index": 0}]], "error": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "reddit-story-video-generator", "tags": []}