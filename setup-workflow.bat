@echo off
echo Setting up Reddit Story Video Workflow...

REM Create required directories
if not exist "audio" mkdir audio
if not exist "temp" mkdir temp
if not exist "output" mkdir output
if not exist "snapchat-ready" mkdir snapchat-ready
if not exist "videos" mkdir videos

echo Directories created successfully.

REM Check if FFmpeg is installed
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: FFmpeg is not installed or not in PATH.
    echo Please install FFmpeg from https://ffmpeg.org/download.html
    echo and add it to your system PATH.
    pause
    exit /b 1
)

echo FFmpeg is installed.

REM Check if a sample video exists
if not exist "videos\minecraft.mp4" (
    echo WARNING: No minecraft.mp4 found in videos folder.
    echo You need to add a Minecraft gameplay video to videos\minecraft.mp4
    echo You can download one from YouTube or record your own.
    echo.
    echo For now, creating a test video...
    
    REM Create a simple test video if FFmpeg is available
    ffmpeg -f lavfi -i testsrc=duration=600:size=1080x1920:rate=30 -f lavfi -i sine=frequency=1000:duration=600 -c:v libx264 -c:a aac -shortest "videos\minecraft.mp4" >nul 2>&1
    
    if exist "videos\minecraft.mp4" (
        echo Test video created successfully.
    ) else (
        echo Failed to create test video. Please add your own minecraft.mp4 file.
    )
)

echo.
echo Setup complete! 
echo.
echo Next steps:
echo 1. Make sure you have valid API credentials for:
echo    - Google Gemini API (for text rewriting)
echo    - ElevenLabs API (for text-to-speech)
echo.
echo 2. Configure these credentials in your n8n instance
echo.
echo 3. Add a proper Minecraft gameplay video to videos\minecraft.mp4
echo    (The current test video is just a placeholder)
echo.
echo 4. Import the workflow into n8n and test it
echo.
pause
