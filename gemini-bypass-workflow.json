{"name": "Reddit Video Generator (Gemini Bypass)", "nodes": [{"parameters": {}, "id": "manual-trigger", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"method": "GET", "url": "https://www.reddit.com/r/AmItheAsshole/top/.json?limit=10&t=day", "options": {"headers": {"User-Agent": "n8n-reddit-scraper/1.0"}}}, "id": "scrape-reddit", "name": "Scrape Reddit", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 300]}, {"parameters": {"jsCode": "// Process Reddit data and create engaging story without AI\nconst redditData = $json.data.children;\nlet selectedPost = null;\n\n// Find a good post\nfor (const post of redditData) {\n  const postData = post.data;\n  if (postData.selftext && \n      postData.selftext.length > 500 && \n      postData.selftext.length < 8000 &&\n      postData.score > 50 &&\n      !postData.over_18) {\n    selectedPost = postData;\n    break;\n  }\n}\n\nif (!selectedPost) {\n  throw new Error('No suitable posts found');\n}\n\n// Clean and enhance the text without AI\nlet title = selectedPost.title;\nlet text = selectedPost.selftext;\n\n// Remove Reddit formatting\ntext = text.replace(/\\[.*?\\]/g, '');\ntext = text.replace(/\\(.*?\\)/g, '');\ntext = text.replace(/\\*\\*/g, '');\ntext = text.replace(/\\*/g, '');\ntext = text.replace(/\\n\\n+/g, ' ');\ntext = text.replace(/\\n/g, ' ');\ntext = text.replace(/\\s+/g, ' ');\ntext = text.trim();\n\n// Create engaging story\nlet rewrittenText = `Here's a story that will make you think. ${title}. ${text} What do you think about this situation? Let me know in the comments below.`;\n\n// Trim if too long\nif (rewrittenText.length > 2000) {\n  rewrittenText = rewrittenText.substring(0, 1900) + '... What are your thoughts?';\n}\n\nconst words = rewrittenText.split(/\\s+/).filter(word => word.length > 0);\n\nreturn {\n  json: {\n    original_title: title,\n    rewritten_text: rewrittenText,\n    word_count: words.length,\n    words: words,\n    subreddit: 'AmItheAsshole',\n    processing_method: 'no_ai_bypass',\n    post_score: selectedPost.score,\n    post_comments: selectedPost.num_comments\n  }\n};"}, "id": "process-without-ai", "name": "Process Without AI", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "xi-api-key", "value": "={{ $credentials.elevenlabsApi.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.rewritten_text }}"}, {"name": "model_id", "value": "eleven_multilingual_v2"}]}, "options": {"headers": {"Content-Type": "application/json"}, "response": {"responseFormat": "file"}}}, "id": "generate-audio", "name": "Generate Audio", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [900, 300]}, {"parameters": {"jsCode": "// Save audio and prepare for video generation\nconst fs = require('fs');\nconst path = require('path');\n\n// Create directories\nconst dirs = ['audio', 'temp', 'output'];\ndirs.forEach(dir => {\n  if (!fs.existsSync(dir)) {\n    fs.mkdirSync(dir, { recursive: true });\n  }\n});\n\n// Save audio file\nif ($binary && $binary.data && $binary.data.data) {\n  const timestamp = Date.now();\n  const filename = `bypass_audio_${timestamp}.mp3`;\n  const filepath = path.join('audio', filename);\n  \n  fs.writeFileSync(filepath, $binary.data.data);\n  \n  return {\n    json: {\n      audio_file: filepath,\n      filename: filename,\n      rewritten_text: $('Process Without AI').item.json.rewritten_text,\n      word_count: $('Process Without AI').item.json.word_count,\n      processing_method: 'gemini_bypass_success',\n      message: 'Audio generated successfully without Gemini AI'\n    }\n  };\n} else {\n  throw new Error('Failed to generate audio');\n}"}, "id": "save-audio", "name": "Save Audio", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Scrape Reddit", "type": "main", "index": 0}]]}, "Scrape Reddit": {"main": [[{"node": "Process Without AI", "type": "main", "index": 0}]]}, "Process Without AI": {"main": [[{"node": "Generate Audio", "type": "main", "index": 0}]]}, "Generate Audio": {"main": [[{"node": "Save Audio", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "bypass-workflow", "tags": []}